<template>
  <div class="smart-park-viewer">
    <!-- 3D 场景容器 -->
    <div ref="threeContainer" class="three-container"></div>

    <!-- 扩展 GUI 控制器 -->
    <ThreeGUIController
      v-if="false 
      "
      :three-scene="threeScene"
      :options="guiOptions"
      :visible="showGUI"
      @gui-created="onGUICreated"
      @gui-destroyed="onGUIDestroyed"
      @parameter-changed="onParameterChanged"
    />
    
    <!-- 科技感加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <!-- 科技感加载动画 -->
        <div class="tech-loader">
          <div class="tech-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          <div class="tech-core">
            <div class="core-pulse"></div>
          </div>
        </div>

        <!-- 科技感文字 -->
        <div class="loading-text-container">
          <h2 class="loading-title">NANJING BANK DIGITAL SYSTEM</h2>
          <p class="loading-subtitle">正在初始化智慧园区三维模型...</p>
          <div class="loading-dots">
            <span></span><span></span><span></span>
          </div>
        </div>

        <!-- 科技感进度条 -->
        <div v-if="loadingProgress > 0" class="tech-progress">
          <div class="progress-container">
            <div class="progress-track">
              <div
                class="progress-fill"
                :style="{ width: loadingProgress + '%' }"
              ></div>
              <div class="progress-glow" :style="{ left: loadingProgress + '%' }"></div>
            </div>
            <div class="progress-info">
              <span class="progress-label">LOADING</span>
              <span class="progress-percentage">{{ Math.round(loadingProgress) }}%</span>
            </div>
          </div>

          <!-- 数据流动效果 -->
          <div class="data-stream">
            <div class="stream-line" v-for="i in 5" :key="i" :style="{ animationDelay: i * 0.2 + 's' }"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <div class="error-content">
        <h3>模型加载失败</h3>
        <p>{{ error }}</p>
        <button @click="retryLoad" class="retry-button">重试</button>
      </div>
    </div>



    <!-- 楼层控制面板 -->
    <!-- <div v-if="!loading && !error" class="floor-control-panel">
      <div class="panel-header">
        <h3>楼层控制</h3>
      </div>

      <div class="panel-content">
        <div class="control-group">
          <label>选择楼层</label>
          <select
            v-model="currentFloor"
            @change="switchFloor(currentFloor)"
            class="floor-select"
          >
            <option
              v-for="(value, label) in floorOptions"
              :key="value"
              :value="value"
            >
              {{ label }}
            </option>
          </select>
        </div>

        <div class="control-group">
          <label>快捷按钮</label>
          <div class="button-group">
            <button
              @click="switchFloor('all')"
              :class="{ active: currentFloor === 'all' }"
              class="control-button"
            >
              全部显示
            </button>
            <button
              @click="switchFloor('roof')"
              :class="{ active: currentFloor === 'roof' }"
              class="control-button"
            >
              楼顶
            </button>
            <button
              @click="switchFloor('floor-2')"
              :class="{ active: currentFloor === 'floor-2' }"
              class="control-button"
            >
              2楼
            </button>
            <button
              @click="switchFloor('floor-1')"
              :class="{ active: currentFloor === 'floor-1' }"
              class="control-button"
            >
              1楼
            </button>
          </div>
        </div>
      </div>
    </div> -->

    <!-- 快捷键提示 -->
    <div v-if="showKeyboardHelp" class="keyboard-help">
      <h4>快捷键</h4>
      <ul>
        <li><kbd>G</kbd> - 切换 GUI 显示/隐藏</li>
        <li><kbd>R</kbd> - 重置相机位置</li>
        <li><kbd>W</kbd> - 切换线框模式</li>
        <li><kbd>N</kbd> - 切换昼夜模式</li>
        <li><kbd>K</kbd> - 切换动态天空盒</li>
        <li><kbd>S</kbd> - 导出截图</li>
        <li><kbd>F</kbd> - 切换全屏</li>
        <li><kbd>?</kbd> - 显示/隐藏此帮助</li>
      </ul>
    </div>

    <!-- 控制按钮 -->
    <div class="control-buttons">
      <button @click="toggleGUI" class="control-btn" title="切换 GUI (G)">
        <span v-if="showGUI">🎛️</span>
        <span v-else>📱</span>
      </button>
      <button @click="toggleKeyboardHelp" class="control-btn" title="快捷键帮助 (?)">
        ❓
      </button>
      <button @click="resetAll" class="control-btn" title="重置所有 (R)">
        🔄
      </button>
      <button @click="exportScreenshot" class="control-btn" title="截图 (S)">
        📷
      </button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import * as THREE from 'three'
import { ThreeScene } from '../../utils/ThreeScene.js'
import ThreeGUIController from './ThreeGUIController.vue'

export default {
  name: 'SmartParkViewer',
  components: {
    ThreeGUIController
  },
  props: {
    modelPath: {
      type: String,
      default: './model/整体模型.glb'
    },
    useTestCube: {
      type: Boolean,
      default: false // 默认使用实际模型文件
    }
  },
  setup(props) {
    const threeContainer = ref(null)
    const threeScene = ref(null)
    const loading = ref(true)
    const loadingProgress = ref(0)
    const error = ref(null)
    const autoRotate = ref(false)
    const showWireframe = ref(false)
    const modelInfo = ref({})
    const isFullscreen = ref(false)
    const showGUI = ref(false)
    const showKeyboardHelp = ref(false)
    const dynamicSkyboxEnabled = ref(true)
    


    // 楼层控制相关
    const currentFloor = ref('all')
    const floorOptions = ref({
      '全部显示': 'all',
      '楼顶': 'roof',
      '2楼': 'floor-2',
      '1楼': 'floor-1'
    })



    const guiOptions = ref({
      width: 350,
      position: 'top-right',
      autoPlace: true,
      closed: false
    })

    /**
     * 初始化 3D 场景
     */
    const initThreeScene = async () => {
      try {
        loading.value = true
        error.value = null

        // 确保DOM元素已经渲染
        await new Promise(resolve => setTimeout(resolve, 100))

        // 检查容器是否存在且有尺寸
        if (!threeContainer.value) {
          throw new Error('Three.js 容器未找到')
        }

        console.log('容器元素:', threeContainer.value)
        console.log('容器尺寸:', {
          clientWidth: threeContainer.value.clientWidth,
          clientHeight: threeContainer.value.clientHeight,
          offsetWidth: threeContainer.value.offsetWidth,
          offsetHeight: threeContainer.value.offsetHeight
        })

        // 创建 Three.js 场景（不启用内置 GUI，使用扩展 GUI）
        threeScene.value = new ThreeScene(threeContainer.value, false)

        // 不自动加载天空盒，可通过GUI控制
        // await loadSkybox()

        // 加载模型
        await loadModel()



      } catch (err) {
        console.error('场景初始化失败:', err)
        error.value = err.message || '场景初始化失败'
      } finally {
        loading.value = false
      }
    }

    // 天空盒加载功能已移至GUI控制，不再自动加载

    /**
     * 加载 3D 模型或创建测试场景
     */
    const loadModel = async () => {
      try {
        if (props.useTestCube) {
          // 使用测试立方体
          loadingProgress.value = 50
          await new Promise(resolve => setTimeout(resolve, 500)) // 模拟加载时间
          loadingProgress.value = 100
          const testScene = await threeScene.value.createTestCube()

          // 收集模型信息
          collectModelInfo(testScene)
          console.log('测试场景创建成功')
        } else {
          // 加载实际模型
          const gltf = await threeScene.value.loadModel(
            props.modelPath,
            (progress) => {
              if (progress.lengthComputable) {
                loadingProgress.value = (progress.loaded / progress.total) * 100
              }
            }
          )

          // 收集模型信息
          collectModelInfo(gltf)

          // 初始化楼层选项
          initFloorOptions()

          console.log('模型加载成功:', gltf)
        }
      } catch (err) {
        throw new Error(`模型加载失败: ${err.message}`)
      }
    }

    /**
     * 收集模型信息
     */
    const collectModelInfo = (gltf) => {
      let triangles = 0
      let vertices = 0
      const materials = new Set()

      gltf.scene.traverse((child) => {
        if (child.isMesh) {
          if (child.geometry) {
            const geometry = child.geometry
            if (geometry.index) {
              triangles += geometry.index.count / 3
            } else {
              triangles += geometry.attributes.position.count / 3
            }
            vertices += geometry.attributes.position.count
          }

          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach(mat => materials.add(mat.uuid))
            } else {
              materials.add(child.material.uuid)
            }
          }
        }
      })

      modelInfo.value = {
        triangles: Math.round(triangles),
        vertices: vertices,
        materials: materials.size
      }
    }

    /**
     * 初始化楼层选项
     */
    const initFloorOptions = () => {
      if (threeScene.value) {
        const floorManager = threeScene.value.getFloorManager()
        if (floorManager) {
          // 获取楼层管理器的选项
          const options = floorManager.getFloorOptions()
          floorOptions.value = options
          console.log('楼层选项已初始化:', options)
        }
      }
    }

    /**
     * 重置相机位置
     */
    const resetCamera = () => {
      if (threeScene.value) {
        const controls = threeScene.value.getControls()
        const camera = threeScene.value.getCamera()
        const model = threeScene.value.getModel()

        if (model) {
          // 如果有模型，根据模型大小重新计算相机位置
          const box = new THREE.Box3().setFromObject(model)
          const size = box.getSize(new THREE.Vector3())
          const maxDim = Math.max(size.x, size.y, size.z)

          const distance = maxDim * 0.8
          camera.position.set(distance * 0.8, distance * 0.6, distance * 0.8)
          camera.lookAt(0, 0, 0)
          controls.target.set(0, 0, 0)
        } else {
          // 没有模型时使用新的默认位置
          camera.position.set(-67.053, 71.188, -83.492)
          camera.rotation.set(-0.972, 0.036, 0.052)
          camera.lookAt(-70.134, 0, -132.082)
          controls.target.set(-70.134, 0, -132.082)
        }

        controls.update()
      }
    }

    /**
     * 切换自动旋转
     */
    const toggleAutoRotate = () => {
      if (threeScene.value) {
        const controls = threeScene.value.getControls()
        autoRotate.value = !autoRotate.value
        controls.autoRotate = autoRotate.value
        controls.autoRotateSpeed = 1.0
      }
    }

    /**
     * 切换线框模式
     */
    const toggleWireframe = () => {
      if (threeScene.value) {
        const model = threeScene.value.getModel()
        if (model) {
          model.traverse((child) => {
            if (child.isMesh && child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach(mat => {
                  mat.wireframe = showWireframe.value
                })
              } else {
                child.material.wireframe = showWireframe.value
              }
            }
          })
        }
      }
    }



    /**
     * 重试加载
     */
    const retryLoad = () => {
      error.value = null
      loadingProgress.value = 0
      initThreeScene()
    }

    /**
     * 切换全屏模式
     */
    const toggleFullscreen = async () => {
      try {
        if (!document.fullscreenElement) {
          // 进入全屏
          await threeContainer.value.requestFullscreen()
          isFullscreen.value = true
        } else {
          // 退出全屏
          await document.exitFullscreen()
          isFullscreen.value = false
        }
      } catch (err) {
        console.error('全屏切换失败:', err)
      }
    }

    /**
     * 切换楼层显示
     */
    const switchFloor = (targetFloor) => {
      if (threeScene.value) {
        const floorManager = threeScene.value.getFloorManager()
        if (floorManager) {
          floorManager.switchToFloor(targetFloor)
          currentFloor.value = targetFloor
          console.log(`已切换到楼层: ${targetFloor}`)
        } else {
          console.warn('楼层管理器未初始化')
        }
      }
    }









    /**
     * GUI 创建完成回调
     */
    const onGUICreated = (guiController) => {
      console.log('GUI 控制器已创建')
    }

    /**
     * GUI 销毁回调
     */
    const onGUIDestroyed = () => {
      console.log('GUI 控制器已销毁')
    }

    /**
     * 参数变化回调
     */
    const onParameterChanged = (changeInfo) => {
      console.log('参数变化:', changeInfo)
    }

    /**
     * 切换 GUI 显示
     */
    const toggleGUI = () => {
      showGUI.value = !showGUI.value
    }

    /**
     * 切换键盘帮助
     */
    const toggleKeyboardHelp = () => {
      showKeyboardHelp.value = !showKeyboardHelp.value
    }

    /**
     * 重置所有参数
     */
    const resetAll = () => {
      if (threeScene.value) {
        // 重置相机到新的默认位置
        threeScene.value.camera.position.set(-67.053, 71.188, -83.492)
        threeScene.value.camera.rotation.set(-0.972, 0.036, 0.052)
        threeScene.value.camera.lookAt(-70.134, 0, -132.082)
        threeScene.value.controls.target.set(-70.134, 0, -132.082)
        threeScene.value.controls.update()
      }
    }

    /**
     * 导出截图
     */
    const exportScreenshot = () => {
      if (threeScene.value) {
        const canvas = threeScene.value.renderer.domElement
        const link = document.createElement('a')
        link.download = `screenshot_${Date.now()}.png`
        link.href = canvas.toDataURL()
        link.click()
      }
    }

    /**
     * 切换昼夜模式
     */
    const toggleDayNight = () => {
      if (threeScene.value) {
        threeScene.value.toggleDayNightMode()
      }
    }

    /**
     * 切换动态天空盒
     */
    const toggleDynamicSkybox = () => {
      if (threeScene.value) {
        dynamicSkyboxEnabled.value = !dynamicSkyboxEnabled.value
        threeScene.value.toggleDynamicSkybox(dynamicSkyboxEnabled.value)
      }
    }

    /**
     * 监听全屏状态变化
     */
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement
      // 全屏状态改变时，需要重新调整渲染器尺寸
      if (threeScene.value) {
        setTimeout(() => {
          threeScene.value.onWindowResize()
        }, 100)
      }
    }

    /**
     * 键盘事件处理
     */
    const handleKeyDown = (event) => {
      // 处理 Ctrl+H 切换GUI
      if ((event.ctrlKey || event.metaKey) && event.key.toLowerCase() === 'h') {
        event.preventDefault()
        toggleGUI()
        return
      }

      if (event.ctrlKey || event.metaKey) return

      switch (event.key.toLowerCase()) {
        case '?':
          toggleKeyboardHelp()
          break
        case 'r':
          resetAll()
          break
        case 's':
          exportScreenshot()
          break
        case 'f':
          toggleFullscreen()
          break
        case 'w':
          showWireframe.value = !showWireframe.value
          toggleWireframe()
          break
        case 'n':
          toggleDayNight()
          break
        case 'k':
          toggleDynamicSkybox()
          break
      }
    }

    /**
     * 处理来自父页面的postMessage
     */
    const handlePostMessage = (event) => {
      // 安全检查：确保消息来源正确（可根据实际部署环境调整）
      // if (event.origin !== 'http://localhost:3000') {
      //   return;
      // }

      const { type, floor, timestamp } = event.data;

      if (type === 'FLOOR_SWITCH') {
        console.log('收到楼层切换指令:', floor);

        try {
          // 执行楼层切换
          switchFloor(floor);

          // 发送成功消息回父页面
          window.parent.postMessage({
            type: 'FLOOR_SWITCH_COMPLETE',
            data: {
              floor: floor,
              timestamp: Date.now(),
              originalTimestamp: timestamp
            }
          }, '*');

        } catch (error) {
          console.error('楼层切换失败:', error);

          // 发送错误消息回父页面
          window.parent.postMessage({
            type: 'FLOOR_SWITCH_ERROR',
            data: {
              floor: floor,
              error: error.message || '未知错误',
              timestamp: Date.now(),
              originalTimestamp: timestamp
            }
          }, '*');
        }
      }
    }

    /**
     * 通知父页面模型已准备就绪
     */
    const notifyModelReady = () => {
      window.parent.postMessage({
        type: 'MODEL_READY',
        data: {
          timestamp: Date.now()
        }
      }, '*');
    }

    // 生命周期钩子
    onMounted(() => {
      // 添加全屏状态监听
      document.addEventListener('fullscreenchange', handleFullscreenChange)
      // 添加键盘事件监听
      document.addEventListener('keydown', handleKeyDown)
      // 添加postMessage监听器
      window.addEventListener('message', handlePostMessage)

      // 初始化Three.js场景
      initThreeScene()

      // 延迟通知父页面模型已准备就绪
      setTimeout(() => {
        notifyModelReady()
      }, 3000) // 延迟3秒确保模型完全加载
    })

    onUnmounted(() => {
      // 移除全屏状态监听
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      // 移除键盘事件监听
      document.removeEventListener('keydown', handleKeyDown)
      // 移除postMessage监听器
      window.removeEventListener('message', handlePostMessage)


      if (threeScene.value) {
        threeScene.value.dispose()
      }
    })

    return {
      threeContainer,
      threeScene,
      loading,
      loadingProgress,
      error,
      autoRotate,
      showWireframe,
      modelInfo,
      isFullscreen,
      currentFloor,
      floorOptions,
      showGUI,
      showKeyboardHelp,
      guiOptions,
      resetCamera,
      toggleAutoRotate,
      toggleWireframe,
      toggleFullscreen,
      switchFloor,

      toggleGUI,
      toggleKeyboardHelp,
      resetAll,
      exportScreenshot,
      toggleDayNight,
      onGUICreated,
      onGUIDestroyed,
      onParameterChanged,
      retryLoad
    }
  }
}
</script>

<style scoped>
.smart-park-viewer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #1a1a1a;
  margin: 0;
  padding: 0;
}

.three-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 全屏状态下的样式 */
.smart-park-viewer:fullscreen {
  width: 100vw;
  height: 100vh;
}

.smart-park-viewer:fullscreen .three-container {
  width: 100vw;
  height: 100vh;
}

/* 科技感加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: radial-gradient(ellipse at center, rgba(0, 20, 40, 0.95) 0%, rgba(0, 0, 0, 0.98) 100%);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 24vh;
  z-index: 1000;
  overflow: hidden;
}

.loading-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, transparent 30%, rgba(0, 188, 212, 0.03) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(0, 188, 212, 0.03) 50%, transparent 70%);
  animation: techGrid 4s linear infinite;
}

@keyframes techGrid {
  0% { transform: translate(-50%, -50%); }
  100% { transform: translate(50%, 50%); }
}

.loading-content {
  text-align: center;
  color: white;
  position: relative;
  z-index: 10;
}

/* 科技感加载器 */
.tech-loader {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 40px;
}

.tech-spinner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.spinner-ring {
  position: absolute;
  border: 2px solid transparent;
  border-radius: 50%;
  animation: techSpin 3s linear infinite;
}

.spinner-ring:nth-child(1) {
  width: 120px;
  height: 120px;
  border-top: 2px solid #00bcd4;
  border-right: 2px solid #00bcd4;
  animation-duration: 2s;
}

.spinner-ring:nth-child(2) {
  width: 90px;
  height: 90px;
  top: 15px;
  left: 15px;
  border-bottom: 2px solid #4caf50;
  border-left: 2px solid #4caf50;
  animation-duration: 1.5s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 60px;
  height: 60px;
  top: 30px;
  left: 30px;
  border-top: 2px solid #ff9800;
  border-right: 2px solid #ff9800;
  animation-duration: 1s;
}

@keyframes techSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.tech-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
}

.core-pulse {
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, #00bcd4 0%, transparent 70%);
  border-radius: 50%;
  animation: corePulse 2s ease-in-out infinite;
}

@keyframes corePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.7;
  }
}

/* 科技感文字 */
.loading-text-container {
  margin-bottom: 40px;
}

.loading-title {
  font-size: 24px;
  font-weight: 300;
  letter-spacing: 4px;
  color: #00bcd4;
  margin-bottom: 10px;
  text-shadow: 0 0 10px rgba(0, 188, 212, 0.5);
  font-family: 'Courier New', monospace;
}

.loading-subtitle {
  font-size: 14px;
  color: #ccc;
  margin-bottom: 20px;
  opacity: 0.8;
}

.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: #00bcd4;
  border-radius: 50%;
  animation: dotPulse 1.5s ease-in-out infinite;
}

.loading-dots span:nth-child(2) { animation-delay: 0.2s; }
.loading-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes dotPulse {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.2); }
}

/* 科技感进度条 */
.tech-progress {
  width: 400px;
  margin: 0 auto;
  position: relative;
}

.progress-container {
  margin-bottom: 20px;
}

.progress-track {
  position: relative;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg,
    #00bcd4 0%,
    #4caf50 50%,
    #00bcd4 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
  box-shadow: 0 0 15px rgba(0, 188, 212, 0.6);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-glow {
  position: absolute;
  top: -2px;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #00bcd4 0%, transparent 70%);
  border-radius: 50%;
  transform: translateX(-50%);
  animation: glowPulse 1s ease-in-out infinite;
}

@keyframes glowPulse {
  0%, 100% { opacity: 0.8; transform: translateX(-50%) scale(1); }
  50% { opacity: 1; transform: translateX(-50%) scale(1.2); }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  font-family: 'Courier New', monospace;
}

.progress-label {
  font-size: 12px;
  color: #00bcd4;
  letter-spacing: 2px;
  text-shadow: 0 0 5px rgba(0, 188, 212, 0.5);
}

.progress-percentage {
  font-size: 18px;
  color: #fff;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* 数据流动效果 */
.data-stream {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.stream-line {
  position: absolute;
  width: 2px;
  height: 20px;
  background: linear-gradient(to bottom,
    transparent 0%,
    #00bcd4 50%,
    transparent 100%);
  animation: streamFlow 3s linear infinite;
  opacity: 0.7;
}

.stream-line:nth-child(1) { left: 10%; }
.stream-line:nth-child(2) { left: 30%; }
.stream-line:nth-child(3) { left: 50%; }
.stream-line:nth-child(4) { left: 70%; }
.stream-line:nth-child(5) { left: 90%; }

@keyframes streamFlow {
  0% {
    top: -20px;
    opacity: 0;
  }
  10% {
    opacity: 0.7;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    top: 100%;
    opacity: 0;
  }
}



/* 错误状态样式 */
.error-overlay {
  position: fixed;  /* 使用fixed定位 */
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(26, 26, 26, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.error-content {
  text-align: center;
  color: white;
  max-width: 400px;
  padding: 20px;
}

.error-content h3 {
  color: #f44336;
  margin-bottom: 15px;
}

.retry-button {
  background: #00bcd4;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
  transition: background 0.3s ease;
}

.retry-button:hover {
  background: #0097a7;
}

/* 楼层控制面板样式 */
.floor-control-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  background: rgba(0, 0, 0, 0.85);
  border-radius: 12px;
  color: white;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 188, 212, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 100;
  pointer-events: auto;
  transition: all 0.3s ease;
}

.floor-control-panel:hover {
  border-color: rgba(0, 188, 212, 0.5);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.panel-header {
  padding: 18px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(0, 188, 212, 0.1) 0%, rgba(0, 188, 212, 0.05) 100%);
  border-radius: 12px 12px 0 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #00bcd4;
  text-shadow: 0 0 10px rgba(0, 188, 212, 0.3);
  letter-spacing: 1px;
}

.panel-content {
  padding: 24px;
}

.control-group {
  margin-bottom: 24px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #e0e0e0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 楼层选择框样式 */
.floor-select {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.floor-select:focus {
  outline: none;
  border-color: #00bcd4;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.floor-select option {
  background: #2a2a2a;
  color: white;
  padding: 8px;
}

.button-group {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.control-button {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.control-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.control-button:hover::before {
  left: 100%;
}

.control-button:hover {
  background: linear-gradient(135deg, rgba(0, 188, 212, 0.2) 0%, rgba(0, 188, 212, 0.1) 100%);
  border-color: #00bcd4;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
}

.control-button.active {
  background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
  border-color: #00bcd4;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 188, 212, 0.4);
  transform: translateY(-1px);
}

.control-button.active:hover {
  background: linear-gradient(135deg, #00acc1 0%, #00838f 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 188, 212, 0.5);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 13px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  accent-color: #00bcd4;
}

.info-text {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
}

.info-text p {
  margin: 5px 0;
}

/* 快捷键帮助样式 */
.keyboard-help {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 20px;
  border-radius: 8px;
  z-index: 1001;
  min-width: 300px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(0, 188, 212, 0.3);
}

.keyboard-help h4 {
  margin-top: 0;
  color: #00bcd4;
  text-align: center;
  margin-bottom: 15px;
}

.keyboard-help ul {
  list-style: none;
  padding: 0;
}

.keyboard-help li {
  margin: 8px 0;
  display: flex;
  align-items: center;
}

.keyboard-help kbd {
  background: #333;
  border: 1px solid #555;
  border-radius: 3px;
  padding: 2px 6px;
  margin-right: 10px;
  font-family: monospace;
  min-width: 20px;
  text-align: center;
  color: #00bcd4;
}

/* 控制按钮样式 */
.control-buttons {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 999;
  display: none;
}

.control-btn {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn:hover {
  background: rgba(0, 188, 212, 0.8);
  transform: scale(1.1);
  border-color: #00bcd4;
  box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
}
</style>
